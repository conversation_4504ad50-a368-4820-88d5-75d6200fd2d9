import { useState, useRef, useEffect } from "react";
import { countryCodes, type CountryCode } from "@/data/countryCodes";

interface CountryCodeSelectorProps {
  selectedCountry: CountryCode;
  onCountryChange: (country: CountryCode) => void;
  className?: string;
  disabled?: boolean;
}

export function CountryCodeSelector({
  selectedCountry,
  onCountryChange,
  className = "",
  disabled = false,
}: CountryCodeSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter countries based on search term
  const filteredCountries = countryCodes.filter(
    (country) =>
      country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      country.dialCode.includes(searchTerm) ||
      country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchTerm("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleCountrySelect = (country: CountryCode) => {
    onCountryChange(country);
    setIsOpen(false);
    setSearchTerm("");
  };

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setSearchTerm("");
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Selected Country Button */}
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={`
          flex items-center justify-between px-3 py-3 rounded-l-lg border-2 border-r-0
          bg-white/80 dark:bg-gray/20 text-dark dark:text-white font-body
          theme-transition focus:outline-none focus:ring-2 focus:ring-dark/50
          dark:focus:ring-light/50 transition-all duration-200 min-w-[90px] w-[90px]
          ${
            disabled
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-white/90 dark:hover:bg-gray/30 cursor-pointer"
          }
          border-gray/30 dark:border-light/30 focus:border-dark dark:focus:border-light
        `}
        aria-label={`Selected country: ${selectedCountry.name} (${selectedCountry.dialCode})`}
      >
        <div className="flex items-center space-x-1">
          <span className="text-base">{selectedCountry.flag}</span>
          <span className="text-xs font-medium">
            {selectedCountry.dialCode}
          </span>
        </div>
        <svg
          className={`w-3 h-3 transition-transform duration-200 flex-shrink-0 ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 z-50 mt-1 bg-white dark:bg-gray-800 border-2 border-gray/30 dark:border-light/30 rounded-lg shadow-xl w-80 max-w-[calc(100vw-2rem)] sm:max-w-[90vw] overflow-hidden theme-transition">
          {/* Search Input */}
          <div className="p-4 border-b border-gray/20 dark:border-light/20">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search countries..."
                className="w-full pl-9 pr-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 border border-gray/30 dark:border-light/30 rounded-md focus:outline-none focus:ring-2 focus:ring-dark/50 dark:focus:ring-light/50 text-dark dark:text-white theme-transition"
              />
              <svg
                className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 dark:text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>

          {/* Countries List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredCountries.length > 0 ? (
              filteredCountries.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  onClick={() => handleCountrySelect(country)}
                  className={`
                    w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-100
                    dark:hover:bg-gray-700 transition-colors duration-150 text-dark dark:text-white
                    min-h-[48px] touch-manipulation
                    ${
                      selectedCountry.code === country.code
                        ? "bg-gray-100 dark:bg-gray-700"
                        : ""
                    }
                  `}
                >
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <span className="text-lg flex-shrink-0">
                      {country.flag}
                    </span>
                    <span className="text-sm font-medium flex-shrink-0 min-w-[45px]">
                      {country.dialCode}
                    </span>
                    <span className="text-sm truncate">{country.name}</span>
                  </div>
                </button>
              ))
            ) : (
              <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                No countries found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
