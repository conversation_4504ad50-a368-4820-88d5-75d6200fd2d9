// API service for handling business audit requests
import type { WelcomeData } from '@/components/webApp/phases/Phase0';
import type { BusinessProfileData } from '@/components/webApp/phases/Phase1';
import type { ChallengesData } from '@/components/webApp/phases/Phase2';
import type { Lead, CreateLeadPayload } from '@/domain/lead';
import type { Report } from '@/domain/report';

export interface GPTResponse {
  id: number;
  leadId: number;
  data: Report;
}

export interface CreateLeadOutput {
  Lead: Lead;
  GPTResponse: GPTResponse;
}

export interface ApiError {
  message: string;
  status?: number;
}

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || window.location.origin;
  }

  async createLead(data: CreateLeadPayload): Promise<CreateLeadOutput> {
    try {
      const response = await fetch(`${this.baseUrl}/api/leads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        try {
          const errorJson = JSON.parse(errorText);
          errorMessage = errorJson.message || errorMessage;
        } catch {
          // If not JSON, use the text as is
          errorMessage = errorText || errorMessage;
        }

        throw new ApiError(errorMessage, response.status);
      }

      const result = await response.json();
      return result as CreateLeadOutput;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // Handle network errors or other issues
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new ApiError('Network error: Unable to connect to the server. Please check your internet connection and try again.');
      }

      throw new ApiError(error instanceof Error ? error.message : 'An unexpected error occurred');
    }
  }
}

// Create a singleton instance
export const apiService = new ApiService();

// Custom error class for API errors
export class ApiError extends Error {
  status?: number;

  constructor(message: string, status?: number) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
  }
}

// Utility functions for data transformation

export interface WebAppFormData {
  phase0?: WelcomeData;
  phase1?: BusinessProfileData;
  phase2?: ChallengesData;
}

export function transformFormDataToLeadRequest(formData: WebAppFormData): CreateLeadPayload {
  if (!formData.phase0 || !formData.phase1 || !formData.phase2) {
    throw new Error('Incomplete form data: All phases must be completed');
  }

  return {
    companyName: formData.phase0.companyName,
    email: formData.phase0.email,
    fullName: formData.phase0.fullName,
    phone: formData.phase0.phone,
    website: formData.phase0.website || '',
    businessType: formData.phase1.businessType,
    employeeCount: formData.phase1.employeeCount,
    industry: formData.phase1.industry,
    monthlyRevenue: formData.phase1.monthlyRevenue,
    salesLocation: formData.phase1.salesLocation,
    bottlenecks: formData.phase2.bottlenecks,
    timeConsumingTasks: formData.phase2.timeConsumingTasks,
  };
}
