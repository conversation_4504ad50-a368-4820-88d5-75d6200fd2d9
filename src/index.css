@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

html {
  -webkit-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

/* Global text wrapping fixes for mobile */
* {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: none;
}

/* Specific text wrappingc for content areas */
p,
span,
div,
h1,
h2,
h3,
h4,
h5,
h6 {
  -webkit-hyphens: none;
  -ms-hyphens: none;
  word-break: normal;
  overflow-wrap: break-word;
  hyphens: none;
}

:root {
  --btn-bg: var(--color-dark);
  --btn-text: var(--color-white);
  --btn-hover: var(--color-gray);

  --card-bg: rgba(255, 255, 255, 0.8);
  --card-text: var(--color-dark);
  --card-label: var(--color-dark);
}

.dark {
  --btn-bg: var(--color-light);
  --btn-text: var(--color-dark);
  --btn-hover: var(--color-gray);

  --card-bg: rgba(28, 33, 32, 0.8);
  --card-text: var(--color-white);
  --card-label: var(--color-light);
}

@theme inline {
  --color-btn-bg: var(--btn-bg);
  --color-btn-text: var(--btn-text);
  --color-btn-hover: var(--btn-hover);

  --color-card-bg: var(--card-bg);
  --color-card-text: var(--card-text);
  --color-card-label: var(--card-label);
}

@theme {
  --color-dark: #1c2120;
  --color-gray: #838485;
  --color-light: #dcdcdc;
  --color-white: #ffffff;

  --font-brand: "League Spartan", sans-serif;
  --font-title: "Poppins", sans-serif;
  --font-body: "Poppins", sans-serif;

  --animate-gradient-shift: gradientShift 8s ease-in-out infinite;
  --animate-gradient-move: gradientMove 12s linear infinite;
  --animate-drift: drift 6s ease-in-out infinite;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes drift {
  0% {
    transform: translateX(0px) translateY(0px);
  }
  25% {
    transform: translateX(10px) translateY(-5px);
  }
  50% {
    transform: translateX(-5px) translateY(-10px);
  }
  75% {
    transform: translateX(-10px) translateY(-5px);
  }
  100% {
    transform: translateX(0px) translateY(0px);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
    transform: translate3d(0, 0, 0);
  }
  25% {
    background-position: 100% 50%;
    transform: translate3d(0, 0, 0);
  }
  50% {
    background-position: 100% 100%;
    transform: translate3d(0, 0, 0);
  }
  75% {
    background-position: 0% 100%;
    transform: translate3d(0, 0, 0);
  }
  100% {
    background-position: 0% 50%;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes gradientMove {
  0% {
    transform: translateX(-100%) translateY(-100%);
  }
  50% {
    transform: translateX(100%) translateY(100%);
  }
  100% {
    transform: translateX(-100%) translateY(-100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceSubtle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

.theme-transition {
  transition: all 0.3s ease-in-out;
  will-change: background-color, color, border-color;
}

@keyframes timelineProgress {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

@keyframes timelinePulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes timelineSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.timeline-progress {
  transform-origin: top;
  animation: timelineProgress 0.8s ease-out forwards;
}

.timeline-step-active {
  animation: timelinePulse 2s ease-in-out infinite;
}

.timeline-label {
  animation: timelineSlideIn 0.6s ease-out forwards;
}

.border-3 {
  border-width: 3px;
}

/* Prevent over-scrolling on all devices */
.snap-y {
  overscroll-behavior-y: none;
}

/* Ensure proper scroll containment */
html,
body {
  overscroll-behavior: none;
  /* Prevent iOS Safari bounce scrolling */
  -webkit-overflow-scrolling: touch;
}

/* Apply fixed positioning only to landing page, not web app */
.landing-page-container html,
.landing-page-container body {
  position: fixed;
  width: 100%;
  height: 100%;
}

/* Reset body positioning for desktop */
@media (min-width: 769px) {
  .landing-page-container html,
  .landing-page-container body {
    position: static;
  }
}

/* Mobile viewport height utility class */
.mobile-viewport-height {
  min-height: 100vh;
  min-width: 100vw;
}

/* Use dvh for mobile devices to handle dynamic viewport changes */
@media (max-width: 768px) {
  .mobile-viewport-height {
    min-height: 100dvh;
    min-width: 100dvw;
  }

  /* Override Tailwind's h-screen with dvh for mobile */
  .h-screen {
    height: 100dvh !important;
  }

  .min-h-screen {
    min-height: 100dvh !important;
  }
}

@keyframes timelineProgressHorizontal {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

.timeline-progress-mobile {
  transform-origin: left;
  animation: timelineProgressHorizontal 0.8s ease-out forwards;
}

@media (max-width: 768px) {
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .timeline-mobile-circle {
    min-width: 2.5rem;
    min-height: 2.5rem;
  }

  /* Prevent text overflow on mobile - only for landing page */
  .landing-page-container body {
    overflow-x: hidden;
    /* Prevent over-scrolling on mobile */
    overscroll-behavior: none;
    /* Use dynamic viewport height for better mobile support */
    height: 100dvh;
  }

  /* Mobile viewport height fixes - only for landing page */
  .landing-page-container html {
    height: 100dvh;
    overscroll-behavior: none;
  }

  /* Web app specific mobile styles */
  .web-app-container {
    /* Allow vertical scrolling on mobile */
    overflow-y: auto;
    overflow-x: hidden;
    /* Use dynamic viewport height but allow content to exceed it */
    min-height: 100dvh;
    /* Ensure smooth scrolling on iOS */
    -webkit-overflow-scrolling: touch;
    /* Prevent horizontal bounce but allow vertical scroll */
    overscroll-behavior-x: none;
    overscroll-behavior-y: auto;
  }

  .web-app-container body {
    /* Allow natural height for scrollable content */
    height: auto;
    min-height: 100dvh;
    overflow-x: hidden;
    overflow-y: auto;
  }

  /* Ensure form inputs don't interfere with scrolling */
  .web-app-container input,
  .web-app-container textarea,
  .web-app-container button {
    /* Prevent zoom on focus for iOS */
    font-size: 16px;
    /* Ensure touch events work properly */
    touch-action: manipulation;
  }

  /* Ensure main content area is scrollable */
  .web-app-container main {
    /* Allow content to exceed viewport height */
    height: auto;
    min-height: calc(100dvh - 5rem); /* Account for header height */
    /* Enable smooth scrolling */
    scroll-behavior: smooth;
  }

  /* Fix for iOS Safari scrolling issues */
  .web-app-container {
    /* Prevent iOS Safari from interfering with scroll */
    position: relative;
    /* Ensure proper touch handling */
    touch-action: pan-y;
    /* Force hardware acceleration for smooth scrolling */
    transform: translateZ(0);
    will-change: scroll-position;
  }

  /* Ensure the entire viewport is scrollable */
  @supports (-webkit-touch-callout: none) {
    /* iOS Safari specific fixes */
    .web-app-container {
      /* Prevent bounce scrolling at the top/bottom */
      overscroll-behavior: none;
      /* Ensure momentum scrolling works */
      -webkit-overflow-scrolling: touch;
    }
  }

  /* Prevent over-scrolling in snap container */
  .snap-y {
    overscroll-behavior-y: none;
    /* Use dynamic viewport height */
    height: 100dvh;
    max-height: 100dvh;
  }

  /* Ensure sections use proper mobile viewport height */
  .snap-start {
    overflow-x: hidden;
    /* Use dynamic viewport height for mobile */
    height: 100dvh;
    min-height: 100dvh;
    max-height: 100dvh;
  }

  /* Ensure text wraps properly */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Prevent horizontal scrolling */
  * {
    max-width: 100dvw;
    box-sizing: border-box;
  }

  /* Smooth horizontal scrolling for service cards */
  .overflow-x-auto {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
    scroll-snap-type: none; /* Disable snap for smooth auto-scroll */
  }

  .overflow-x-auto::-webkit-scrollbar {
    display: none; /* WebKit */
  }

  /* Ensure smooth auto-scroll performance */
  .carousel-container {
    will-change: scroll-position;
    transform: translateZ(0); /* Force hardware acceleration */
    backface-visibility: hidden; /* Optimize for mobile */
  }

  /* Reduce animation complexity on mobile for better performance */
  @media (max-width: 768px) and (prefers-reduced-motion: no-preference) {
    .theme-transition {
      transition-duration: 0.2s; /* Faster transitions on mobile */
    }

    /* Optimize gradient animations for mobile */
    [style*="gradientShift"] {
      animation-duration: 16s !important; /* Slower animations for battery life */
    }
  }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

/* Enhanced select dropdown styling for better theme support */
select {
  color-scheme: light dark;
}

.dark select {
  color-scheme: dark;
}

/* Improve option visibility in dark mode */
select option {
  background-color: white;
  color: #1c2120;
}

.dark select option {
  background-color: #838485;
  color: white;
}

/* Ensure proper contrast for disabled options */
select option:disabled {
  color: #838485;
}

.dark select option:disabled {
  color: #dcdcdc;
}
