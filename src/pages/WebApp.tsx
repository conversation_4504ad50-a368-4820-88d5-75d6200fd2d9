import { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  Phase0,
  Phase1,
  Phase2,
  Phase3,
  Phase4,
  WebAppHeader,
} from "@/components/webApp/phases";
import type { WelcomeData } from "@/components/webApp/phases/Phase0";
import type { BusinessProfileData } from "@/components/webApp/phases/Phase1";
import type { ChallengesData } from "@/components/webApp/phases/Phase2";
import { type ReCaptchaRef } from "@/components/shared/ReCaptcha";
import { apiService, transformFormDataToLeadRequest, ApiError, type CreateLeadOutput } from "@/services/api";

interface WebAppFormData {
  phase0?: WelcomeData;
  phase1?: BusinessProfileData;
  phase2?: ChallengesData;
}
const TOTAL_PHASES = 5;

export function WebApp() {
  const [phase, setPhase] = useState(0);
  const [formData, setFormData] = useState<WebAppFormData>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [apiResponse, setApiResponse] = useState<CreateLeadOutput | null>(null);
  const [hasApiError, setHasApiError] = useState(false);
  const [apiErrorMessage, setApiErrorMessage] = useState<string | null>(null);
  const recaptchaRef = useRef<ReCaptchaRef>(null);

  const navigate = useNavigate();

  useEffect(() => {
    console.log(formData);
  }, [formData]);

  const handleBack = () => {
    if (phase > 0) {
      setPhase(phase - 1);
      setCaptchaToken(null);
      setSubmitError(null);
      recaptchaRef.current?.reset();
    }
  };

  const handleNext = () => {
    if (phase < TOTAL_PHASES - 1) {
      setPhase(phase + 1);
    } else {
      setPhase(TOTAL_PHASES);
    }
  };

  const handleSubmit = async () => {
    if (!captchaToken) {
      setSubmitError("Please complete the reCAPTCHA verification");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Transform form data to API request format
      const leadRequest = transformFormDataToLeadRequest(formData);

      console.log("Submitting form data:", leadRequest);

      // Make API call
      const response = await apiService.createLead(leadRequest);

      // Store the response for the report page
      setApiResponse(response);

      // Store in sessionStorage so it persists during navigation
      sessionStorage.setItem('auditResponse', JSON.stringify(response));

      // Move to loading phase
      setPhase(4);

    } catch (error) {
      console.error("API submission error:", error);

      // Set error states for Phase4 to handle gracefully
      setHasApiError(true);

      if (error instanceof ApiError) {
        setApiErrorMessage(error.message);
        setSubmitError(`Submission failed: ${error.message}`);
      } else {
        const errorMessage = error instanceof Error ? error.message : "Submission failed. Please try again.";
        setApiErrorMessage(errorMessage);
        setSubmitError(errorMessage);
      }

      // Still proceed to Phase4 to show the error state
      setPhase(4);

      recaptchaRef.current?.reset();
      setCaptchaToken(null);
    } finally {
      setIsSubmitting(false);
    }
  };

  const setPhase0Data = (data: WelcomeData) => {
    setFormData((prev) => ({ ...prev, phase0: data }));
  };

  const setPhase1Data = (data: BusinessProfileData) => {
    setFormData((prev) => ({ ...prev, phase1: data }));
  };

  const setPhase2Data = (data: ChallengesData) => {
    setFormData((prev) => ({ ...prev, phase2: data }));
  };

  const handleLoadingComplete = () => {
    // Use the lead ID from the API response, or fallback to a default
    const leadId = apiResponse?.Lead?.id || 'default';
    navigate(`/app/report/${leadId}`);
  };

  const renderPhase = () => {
    switch (phase) {
      case 0:
        return (
          <Phase0
            onNext={handleNext}
            formData={formData.phase0}
            setFormData={setPhase0Data}
          />
        );
      case 1:
        return (
          <Phase1
            onNext={handleNext}
            formData={formData.phase1}
            setFormData={setPhase1Data}
          />
        );
      case 2:
        return (
          <Phase2
            onNext={handleNext}
            formData={formData.phase2}
            setFormData={setPhase2Data}
          />
        );
      case 3:
        return (
          <Phase3
            recaptchaRef={recaptchaRef}
            submitError={submitError}
            isSubmitting={isSubmitting}
            captchaToken={captchaToken}
            setCaptchaToken={setCaptchaToken}
            handleFinalSubmit={handleSubmit}
          />
        );
      case 4:
        return (
          <Phase4
            onComplete={handleLoadingComplete}
            hasApiError={hasApiError}
            apiErrorMessage={apiErrorMessage}
          />
        );
      default:
        return (
          <div className="text-center text-gray dark:text-gray">
            Phase not found
          </div>
        );
    }
  };

  return (
    <div className="web-app-container relative min-h-screen overflow-x-hidden theme-transition">
      {/* Header */}
      <WebAppHeader
        currentPhase={Math.min(phase, TOTAL_PHASES - 1)}
        totalPhases={TOTAL_PHASES}
        onBack={handleBack}
        canGoBack={phase > 0 && phase < TOTAL_PHASES}
      />

      {/* Main Content */}
      <main className="relative z-10 pt-20 sm:pt-24 md:pt-28 lg:pt-32 px-4 sm:px-6 lg:px-8 pb-8 min-h-screen">
        <div className="max-w-4xl mx-auto">{renderPhase()}</div>
      </main>
    </div>
  );
}
