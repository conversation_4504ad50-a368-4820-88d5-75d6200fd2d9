export interface Lead {
  id: string;
  companyName: string;
  email: string;
  fullName: string;
  phone: string;
  website: string;
  businessType: string;
  employeeCount: string;
  industry: string;
  monthlyRevenue: string;
  salesLocation: string;
  bottlenecks: string;
  timeConsumingTasks: string;
  createdAt: string;
}

export interface CreateLeadPayload {
  companyName: string;
  email: string;
  fullName: string;
  phone: string;
  website: string;
  businessType: string;
  employeeCount: string;
  industry: string;
  monthlyRevenue: string;
  salesLocation: string;
  bottlenecks: string;
  timeConsumingTasks: string;
}